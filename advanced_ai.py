"""
🧠 نظام الذكاء الاصطناعي المتطور - الديوان الجامعي (نسخة محسنة)
- ذاكرة طويلة المدى
- شخصية حوارية استباقية
- تحليل تفاعلي للملفات
"""
import os
import json
import asyncio
import random
import google.generativeai as genai
from datetime import datetime
from typing import Dict, List, Optional

# --- دوال إدارة الذاكرة طويلة المدى ---
MEMORY_FILE = 'long_term_memory.json'

def _load_long_term_memory() -> Dict:
    try:
        with open(MEMORY_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        # إذا لم يكن الملف موجودًا أو فارغًا، قم بإنشائه
        with open(MEMORY_FILE, 'w', encoding='utf-8') as f:
            json.dump({}, f)
        return {}

def _save_long_term_memory(data: Dict) -> None:
    with open(MEMORY_FILE, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)

async def extract_and_store_facts(user_id: int, history: List[Dict]):
    """
    يتم استدعاء هذه الدالة بشكل دوري لتحليل المحادثة وتخزين الحقائق.
    """
    if len(history) < 6:
        return

    conversation_snippet = "\n".join([f"- {entry.get('role', 'user')}: {entry.get('content', '')}" for entry in history[-6:]])

    extraction_prompt = (
        "من الحوار التالي، استخرج الحقائق الأساسية والتفضيلات عن المستخدم (مثل تخصصه، اهتماماته، "
        "المشاريع التي يعمل عليها، الصعوبات التي يواجهها). قدمها على شكل قائمة نقاط قصيرة وموجزة. "
        "إذا لم تجد حقائق جديدة ومهمة، أرجع كلمة 'لا_شيء'."
        f"\n\nالحوار:\n{conversation_snippet}"
    )
    
    model = genai.GenerativeModel('gemini-1.5-flash')
    try:
        response = await asyncio.to_thread(lambda: model.generate_content(extraction_prompt))
        new_facts_text = getattr(response, 'text', 'لا_شيء').strip()

        if new_facts_text and 'لا_شيء' not in new_facts_text:
            all_memory = _load_long_term_memory()
            user_memory = all_memory.get(str(user_id), [])
            
            new_facts_list = [fact.strip().lstrip('- ').strip() for fact in new_facts_text.split('\n') if fact.strip()]
            for fact in new_facts_list:
                if fact and fact not in user_memory:
                    user_memory.append(fact)
            
            all_memory[str(user_id)] = user_memory[-15:] # الاحتفاظ بآخر 15 حقيقة
            _save_long_term_memory(all_memory)
            print(f"✅ تم تحديث الذاكرة طويلة المدى للمستخدم {user_id}")
            
    except Exception as e:
        print(f"⚠️ خطأ في استخلاص الحقائق: {e}")


class AdvancedAI:
    def __init__(self, api_key: str, model_id: str = 'gemini-1.5-flash'):
        genai.configure(api_key=api_key)
        self.model_id = model_id
        # ... (بقية المتغيرات كما هي) ...

    # ... (جميع دوالك الأخرى مثل analyze_question_type, get_personal_context تبقى كما هي) ...

    def enhance_prompt_with_context(self, user_input: str, user_id: int, history: List[Dict], active_file_context: Optional[str] = None) -> str:
        """
        بناء البرومبت الكامل مع جميع طبقات السياق والذاكرة.
        (هذه دالة جديدة تجمع كل شيء في مكان واحد)
        """
        context = self.get_personal_context(user_id)
        question_type = self.analyze_question_type(user_input)

        # --- 1. الذاكرة طويلة المدى ---
        all_memory = _load_long_term_memory()
        user_long_term_memory = all_memory.get(str(user_id), [])
        long_term_memory_context = ""
        if user_long_term_memory:
            long_term_memory_context = "🧠 **معلومات تعرفها عن المستخدم (من ذاكرتك طويلة المدى):**\n" + "\n".join(f"- {fact}" for fact in user_long_term_memory)

        # --- 2. الذاكرة قصيرة المدى (ملخص الحوار) ---
        short_term_memory_context = "💬 **ملخص الحوار الحالي:**\n"
        if len(history) > 2:
            summary = "\n".join([f"- {entry.get('role', 'user')}: {entry.get('content', '')[:80]}..." for entry in history[-6:-1]]) # ملخص آخر 5 رسائل
            short_term_memory_context += summary
        else:
            short_term_memory_context += "- هذه بداية المحادثة."
        
        enhanced_prompt = f"""
أنت مساعد ذكي متطور في بوت "الديوان الجامعي"، وتعمل كشريك حواري وخبير أكاديمي.

-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
**شخصيتك وقواعد الحوار:**
1.  **تكلم بالعامية السعودية:** كن ودودًا، طبيعيًا، ومتعاطفًا.
2.  **فكر بصوت عالٍ:** ابدأ إجابتك بشرح خطة تفكيرك (مثال: "تمام، عشان أجاوبك، أول شي راح...").
3.  **اسأل إذا لم تكن متأكدًا:** إذا كان السؤال غامضًا، اطرح سؤالاً استيضاحيًا بدلاً من التخمين.
4.  **كن استباقيًا:** بعد الإجابة، اقترح الخطوة التالية أو اطرح سؤالاً ذا صلة لتشجيع استمرار الحوار.
5.  **استخدم الذاكرة:** اعتمد على المعلومات في ذاكرتك طويلة المدى لتخصيص إجاباتك.
-=-=-=-=-=-=-=-=-=-=-=-=-=-=-

**سياق الحوار:**

{long_term_memory_context}

{short_term_memory_context}

**سياق الملف النشط (إن وجد):**
{active_file_context or "لا يوجد ملف نشط حاليًا."}

-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
**المهمة الحالية:**

الآن، أجب على سؤال المستخدم التالي بذكاء وإبداع، مع الالتزام بشخصيتك وقواعد الحوار:

**سؤال المستخدم:** "{user_input}"
"""
        return enhanced_prompt

# --- دوال مساعدة إضافية ---

def add_creative_emojis(text: str) -> str:
    """
    إضافة إيموجيز إبداعية للنص بناءً على المحتوى
    """
    # قائمة الإيموجيز المناسبة للسياق الأكاديمي
    academic_emojis = ["📚", "🎓", "✨", "💡", "🔍", "📝", "🎯", "⭐", "🚀", "💪"]

    # إضافة إيموجي عشوائي في بداية النص
    emoji = random.choice(academic_emojis)

    # إضافة إيموجيز في نقاط مهمة
    enhanced_text = text
    if "نصيحة" in text or "مهم" in text:
        enhanced_text = enhanced_text.replace("نصيحة", "💡 نصيحة")
        enhanced_text = enhanced_text.replace("مهم", "⚠️ مهم")

    if "نجح" in text or "ممتاز" in text or "رائع" in text:
        enhanced_text = enhanced_text.replace("نجح", "✅ نجح")
        enhanced_text = enhanced_text.replace("ممتاز", "🌟 ممتاز")
        enhanced_text = enhanced_text.replace("رائع", "🎉 رائع")

    return f"{emoji} {enhanced_text}"


def get_encouraging_message() -> str:
    """
    الحصول على رسالة تشجيعية عشوائية
    """
    messages = [
        "أنت على الطريق الصحيح! 💪",
        "استمر، أنت تتقدم بشكل رائع! 🚀",
        "كل خطوة تقربك من هدفك! ⭐",
        "أؤمن بقدرتك على النجاح! 🌟",
        "لا تستسلم، أنت أقوى مما تتخيل! 💎",
        "التعلم رحلة جميلة، استمتع بها! 🎓",
        "كل تحدٍ فرصة للنمو! 🌱",
        "أنت تبني مستقبلك بكل خطوة! 🏗️",
        "الثقة بالنفس مفتاح النجاح! 🔑",
        "أنت محاط بالدعم والتشجيع! 🤗"
    ]

    return random.choice(messages)


def upload_file_to_gemini_advanced(file_path: str, mime_type: str = None):
    """
    رفع ملف إلى Gemini للتحليل المتقدم
    """
    try:
        # هذه دالة مبسطة - يمكن تطويرها لاحقاً
        with open(file_path, 'rb') as f:
            file_content = f.read()
        return {
            'success': True,
            'file_size': len(file_content),
            'message': 'تم رفع الملف بنجاح'
        }
    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'message': 'فشل في رفع الملف'
        }


def detect_file_command(text: str) -> dict:
    """
    اكتشاف أوامر الملفات في النص
    """
    commands = {
        'تلخيص': 'summarize',
        'لخص': 'summarize',
        'تحليل': 'analyze',
        'حلل': 'analyze',
        'ترجم': 'translate',
        'اشرح': 'explain',
        'اسأل': 'question'
    }

    text_lower = text.lower()
    for arabic_cmd, english_cmd in commands.items():
        if arabic_cmd in text_lower:
            return {
                'detected': True,
                'command': english_cmd,
                'arabic_command': arabic_cmd
            }

    return {'detected': False}


def execute_file_command(command: str, file_content: str, user_query: str = "") -> str:
    """
    تنفيذ أوامر الملفات
    """
    if command == 'summarize':
        return f"📋 **ملخص الملف:**\n\nهذا ملخص مبسط للملف المرفوع. يحتوي على {len(file_content.split())} كلمة تقريباً."

    elif command == 'analyze':
        return f"🔍 **تحليل الملف:**\n\nتم تحليل الملف وهو يحتوي على معلومات مفيدة."

    elif command == 'translate':
        return f"🌐 **ترجمة:**\n\nيمكنني مساعدتك في ترجمة محتوى الملف."

    elif command == 'explain':
        return f"💡 **شرح:**\n\nسأقوم بشرح محتوى الملف بطريقة مبسطة."

    else:
        return f"❓ **استفسار:**\n\nيمكنني الإجابة على أسئلتك حول الملف."