import os
import json
import asyncio
from datetime import datetime
import google.generativeai as genai
from dotenv import load_dotenv
from functools import wraps
from telegram import Update, ReplyKeyboardMarkup, InlineKeyboardMarkup, InlineKeyboardButton
from telegram.ext import (
    Application,
    CommandHandler,
    MessageHandler,
    filters,
    ContextTypes,
    ConversationHandler,
    CallbackQueryHandler,
)
from telegram.error import BadRequest
try:
    import PyPDF2 # <-- (1) استيراد مكتبة قراءة PDF
except ImportError:
    PyPDF2 = None

# تحميل متغيرات البيئة
load_dotenv('.env')

# --- الإعدادات والمتغيرات الأساسية ---
TELEGRAM_TOKEN = os.getenv('TELEGRAM_TOKEN')
GOOGLE_API_KEY = os.getenv('GOOGLE_API_KEY')
CHANNEL_ID = os.getenv('CHANNEL_ID')
GROUP_ID = int(os.getenv('GROUP_ID'))

if not all([TELEGRAM_TOKEN, GOOGLE_API_KEY, CHANNEL_ID, GROUP_ID]):
    raise ValueError("أحد المتغيرات المطلوبة غير موجود في ملف .env!")

# إعداد Google AI
genai.configure(api_key=GOOGLE_API_KEY)
GEMINI_MODEL_ID = 'gemini-1.5-flash'

# --- (2) استيراد العقل المدبر المطور ---
from advanced_ai import AdvancedAI, extract_and_store_facts, add_creative_emojis, get_encouraging_message

# إنشاء مثيل من الذكاء الاصطناعي المتطور
advanced_ai = AdvancedAI(GOOGLE_API_KEY, GEMINI_MODEL_ID)

# تحميل بيانات التخصصات
try:
    with open('data.json', 'r', encoding='utf-8') as f:
        MAJORS_DATA = json.load(f)
except (FileNotFoundError, json.JSONDecodeError):
    MAJORS_DATA = {}

(AI_CONVERSATION,) = range(1)

# --- إدارة جلسة المستخدم (Bucket) ---
def get_user_ai_bucket(context: ContextTypes.DEFAULT_TYPE) -> dict:
    bucket = context.user_data.get('ai_session')
    if not bucket:
        bucket = {
            'history': [],
            'active_file': None # لتخزين الملف النشط وأجزائه
        }
        context.user_data['ai_session'] = bucket
    return bucket

# --- دالة محسنة للتواصل مع Gemini ---
async def get_gemini_response(prompt: str) -> str:
    try:
        model = genai.GenerativeModel(GEMINI_MODEL_ID)
        def _generate():
            return model.generate_content(prompt)
        response = await asyncio.to_thread(_generate)
        result = getattr(response, 'text', None)
        return result or "آسف، لم أتمكن من توليد رد. حاول مرة أخرى."
    except Exception as e:
        print(f"Error calling Gemini: {e}")
        return "آسف، حدث خطأ تقني أثناء محاولة الإجابة. يرجى المحاولة مرة أخرى."

# --- دوال البوت الأساسية (start, show_majors, etc.) ---
# ... (كل دوالك مثل check_membership, start, show_majors, show_services, button_callback_handler تبقى كما هي بدون تغيير) ...

# --- قسم محادثة الذكاء الاصطناعي المطور ---

@check_membership
async def start_ai_chat(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    # ... (رسالتك الترحيبية الحالية ممتازة، لا داعي للتغيير) ...
    # يمكنك فقط تعديلها لتعكس القدرة على الحوار العميق حول الملفات
    welcome_message = """أهلين بك! 🤖✨

أنا مساعدك الذكي المتطور في الديوان الجامعي، والآن بقدرات حوارية محسنة!

🧠 **أنا أتذكر:** أتذكر محادثاتنا السابقة لأقدم لك مساعدة مخصصة.
📄 **أنا أفهم الملفات بعمق:** أرسل لي ملف PDF، ويمكننا مناقشة محتواه بالتفصيل.
💬 **أنا أحاور:** أنا هنا لأكون شريكك في التفكير، وليس مجرد مجيب على الأسئلة.

جرب تسألني سؤال، أو أرسل ملف PDF للبدء! 🚀"""
    keyboard = [
        ['💡 أمثلة على الأسئلة', '⚙️ إعدادات الجلسة'],
        ['ارجع للقائمة الرئيسية 🔙']
    ]
    await update.message.reply_text(
        welcome_message,
        reply_markup=ReplyKeyboardMarkup(keyboard, resize_keyboard=True)
    )
    return AI_CONVERSATION

async def handle_ai_conversation(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    msg = await update.message.reply_text('أفكر... 🤔')
    
    user_text = update.message.text
    user_id = update.effective_user.id
    bucket = get_user_ai_bucket(context)

    try:
        # --- (الخطوة 1: بناء سياق الملف النشط إن وجد) ---
        active_file_context = None
        if bucket.get('active_file') and bucket['active_file'].get('chunks'):
            active_file = bucket['active_file']
            search_terms = user_text.lower().split()
            relevant_chunks = [
                chunk for chunk in active_file['chunks']
                if any(term in chunk.lower() for term in search_terms)
            ]
            if relevant_chunks:
                context_text = "\n---\n".join(relevant_chunks[:3])
                active_file_context = (
                    f"**معلومات ذات صلة من ملف '{active_file['name']}':**\n{context_text}"
                )

        # --- (الخطوة 2: بناء البرومبت الكامل باستخدام العقل المدبر) ---
        enhanced_prompt = advanced_ai.enhance_prompt_with_context(
            user_input=user_text,
            user_id=user_id,
            history=bucket['history'],
            active_file_context=active_file_context
        )

        # --- (الخطوة 3: الحصول على الإجابة) ---
        response_text = await get_gemini_response(enhanced_prompt)
        
        # --- (الخطوة 4: تحسين الإجابة النهائية) ---
        final_response = add_creative_emojis(response_text) + f"\n\n_{get_encouraging_message()}_"
        await msg.edit_text(final_response, parse_mode='Markdown')

        # --- (الخطوة 5: تحديث الذاكرة) ---
        bucket['history'].append({'role': 'user', 'content': user_text})
        bucket['history'].append({'role': 'model', 'content': response_text})

        # تشغيل تحديث الذاكرة طويلة المدى في الخلفية
        asyncio.create_task(extract_and_store_facts(user_id, bucket['history']))

    except Exception as e:
        print(f"Error in conversation handler: {e}")
        await msg.edit_text("حدث خطأ غير متوقع. 😵‍💫 الرجاء المحاولة مرة أخرى.")

    return AI_CONVERSATION

async def handle_ai_file_message(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    message = update.message
    if not (message.document and message.document.file_name.lower().endswith('.pdf') and PyPDF2):
        await update.message.reply_text("حالياً، أنا أركز على تحليل ملفات PDF فقط. 📄 الرجاء إرسال ملف بصيغة PDF.")
        return AI_CONVERSATION
    
    msg = await update.message.reply_text('جاري قراءة وتحليل ملف الـ PDF... ⏳')
    
    try:
        # --- (الخطوة 1: تنزيل الملف) ---
        tg_file = await message.document.get_file()
        file_name = message.document.file_name
        os.makedirs('downloads', exist_ok=True)
        local_path = os.path.join('downloads', f"tg_{tg_file.file_unique_id}.pdf")
        await tg_file.download_to_drive(custom_path=local_path)

        # --- (الخطوة 2: تقسيم المحتوى إلى أجزاء) ---
        file_content_chunks = []
        with open(local_path, 'rb') as f:
            reader = PyPDF2.PdfReader(f)
            for page in reader.pages:
                page_text = page.extract_text()
                if page_text:
                    # تقسيم كل صفحة إلى فقرات ذات معنى (أكثر من 50 حرفًا)
                    file_content_chunks.extend([chunk for chunk in page_text.split('\n\n') if len(chunk) > 50])
        
        # --- (الخطوة 3: تخزين الأجزاء في جلسة المستخدم) ---
        bucket = get_user_ai_bucket(context)
        bucket['active_file'] = {
            'name': file_name,
            'chunks': file_content_chunks[:150] # نأخذ أول 150 جزء لتجنب التحميل الزائد
        }
        
        # --- (الخطوة 4: توليد رد أولي ذكي) ---
        initial_analysis_prompt = (
            f"لقد قمت للتو بتحميل ملف PDF بعنوان '{file_name}'. "
            "بناءً على هذا العنوان، قم بصياغة رسالة ترحيبية قصيرة وذكية للمستخدم، "
            "واقترح عليه 3 أسئلة عامة ومثيرة للاهتمام يمكن أن يبدأ بها نقاشنا حول الملف."
        )
        initial_response = await get_gemini_response(initial_analysis_prompt)
        
        final_message = f"✅ تم تحميل ومعالجة ملف '{file_name}' بنجاح!\n\n" + initial_response
        await msg.edit_text(final_message)
        
        # تنظيف الملف الذي تم تنزيله
        os.remove(local_path)

    except Exception as e:
        print(f"Error in file handler: {e}")
        await msg.edit_text("حدث خطأ أثناء معالجة الملف. 📂 تأكد من أن الملف سليم وحاول مرة أخرى.")
    
    return AI_CONVERSATION

# --- بقية الدوال مثل reset_session, end_ai_chat تبقى كما هي ---
# ...

def main() -> None:
    app = Application.builder().token(TELEGRAM_TOKEN).build()

    ai_handler = ConversationHandler(
        entry_points=[MessageHandler(filters.Regex('^الدردشة مع الذكاء الاصطناعي 🤖$'), start_ai_chat)],
        states={
            AI_CONVERSATION: [
                MessageHandler(filters.TEXT & ~filters.COMMAND, handle_ai_conversation),
                MessageHandler(filters.Document.PDF, handle_ai_file_message),
                # يمكنك إضافة معالجات أخرى هنا (للصور، الخ)
            ]
        },
        fallbacks=[MessageHandler(filters.Regex('^ارجع للقائمة الرئيسية 🔙$'), end_ai_chat)],
    )

    # ... (بقية الـ Handlers مثل CommandHandler("start") تبقى كما هي) ...

    app.add_handler(ai_handler)
    # ...
    print("🤖 بوت الديوان الجامعي (النسخة المحسنة) يعمل الآن...")
    app.run_polling()

if __name__ == '__main__':
    main()