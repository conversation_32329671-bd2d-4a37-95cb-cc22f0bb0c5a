import os
import json
import asyncio
import uuid
from datetime import datetime
import importlib
import google.generativeai as genai
from dotenv import load_dotenv
from functools import wraps
from telegram import Update, ReplyKeyboardMarkup, InlineKeyboardMarkup, InlineKeyboardButton
from telegram.ext import (
    Application,
    CommandHandler,
    MessageHandler,
    filters,
    ContextTypes,
    ConversationHandler,
    CallbackQueryHandler,
)
from telegram.error import BadRequest

# --- [دمج] --- استيراد مكتبة قراءة PDF
try:
    import PyPDF2
except ImportError:
    PyPDF2 = None

try:
    from reportlab.lib.pagesizes import A4
    from reportlab.pdfgen import canvas
    REPORTLAB_AVAILABLE = True
except Exception:
    REPORTLAB_AVAILABLE = False

# تحميل متغيرات البيئة
load_dotenv('.env')

# --- الإعدادات والمتغيرات الأساسية ---
TELEGRAM_TOKEN = os.getenv('TELEGRAM_TOKEN')
GOOGLE_API_KEY = os.getenv('GOOGLE_API_KEY')
# ... (بقية المتغيرات كما هي) ...
# --- [دمج] --- تعديل بسيط على استيراد `advanced_ai`
# سنستورد الفئة الرئيسية ودالة الذاكرة الجديدة بشكل منفصل
from advanced_ai import AdvancedAI, extract_and_store_facts, add_creative_emojis, get_encouraging_message

# إنشاء مثيل من الذكاء الاصطناعي المتطور
advanced_ai = AdvancedAI(GOOGLE_API_KEY, GEMINI_MODEL_ID)

# ... (بقية الإعدادات وتحميل البيانات كما هي) ...

# --- [دمج] --- تعديل بنية الجلسة (Bucket) لتكون متوافقة مع النظام الجديد
def _get_user_ai_bucket(context: ContextTypes.DEFAULT_TYPE) -> dict:
    bucket = context.user_data.get('ai_session')
    if not bucket:
        bucket = {
            'history': [],
            'name': None,
            'active_file': None, # لتخزين الملف النشط وأجزائه
            # --- ملاحظة: أزلنا 'chat' و 'files' القديمة لتبسيط النموذج ---
        }
        context.user_data['ai_session'] = bucket
    return bucket

# --- [دمج] --- تبسيط دالة استدعاء Gemini لتكون موحدة
async def get_gemini_response(prompt: str) -> str:
    """دالة موحدة ومبسطة للتواصل مع Gemini Flash."""
    try:
        model = genai.GenerativeModel(GEMINI_MODEL_ID)
        def _generate():
            # إعدادات لنموذج حواري ومتوازن
            config = genai.types.GenerationConfig(temperature=0.8, top_p=0.9, max_output_tokens=2048)
            return model.generate_content(prompt, generation_config=config)
        
        response = await asyncio.to_thread(_generate)
        result = getattr(response, 'text', None)
        return result or "آسف، لم أتمكن من توليد رد. حاول مرة أخرى."
    except Exception as e:
        print(f"Error calling Gemini: {e}")
        return "آسف، حدث خطأ تقني أثناء محاولة الإجابة. يرجى المحاولة مرة أخرى."

# --- [حذف] --- تم حذف الدوال القديمة:
# _ensure_user_chat_session, get_advanced_gemini_response, advanced_multimodel_generate_text
# لأننا سنستخدم نظامًا جديدًا وموحدًا داخل handle_ai_conversation.

# --- إدارة الجلسات المحفوظة (تبقى كما هي) ---
# ... (كل دوال _load_all_sessions, _save_user_session, etc. تبقى كما هي تمامًا) ...

# --- مساعد تنزيل الملفات (يبقى كما هو) ---
# ... (دالة download_any_telegram_file تبقى كما هي) ...

# --- دوال البوت الأساسية (start, show_majors, etc. تبقى كما هي) ---
# ... (كل دوال الأوامر والأزرار خارج محادثة الـ AI تبقى كما هي) ...

# --- دوال الذكاء الاصطناعي (هنا التعديلات الرئيسية) ---

@check_membership
async def start_ai_chat(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    user_name = update.effective_user.first_name or "صديقي"
    
    # --- [دمج] --- رسالة ترحيبية جديدة تعكس القدرات الحوارية
    welcome_message = f"""أهلين {user_name}! 🤖✨

أنا مساعدك الذكي المتطور بنسختي الجديدة والأكثر حوارية!

🧠 **أنا أتذكر:** محادثاتنا السابقة تساعدني أقدم لك مساعدة مخصصة لك.
📄 **أنا أفهم الملفات بعمق:** أرسل لي ملف PDF، ويمكننا مناقشة محتواه بالتفصيل كأننا نقرأه سوا.
💬 **أنا أحاور:** أنا هنا لأكون شريكك في التفكير، وليس مجرد مجيب على الأسئلة.

جرب تسألني سؤال، أو أرسل ملف PDF للبدء في نقاش عميق! 🚀"""

    keyboard = [
        ['💡 أمثلة على الأسئلة', '⚙️ إعدادات الجلسة'],
        ['ارجع للقائمة الرئيسية 🔙']
    ]
    await update.message.reply_text(
        welcome_message,
        reply_markup=ReplyKeyboardMarkup(keyboard, resize_keyboard=True)
    )
    return AI_CONVERSION

# --- [دمج] --- معالج المحادثة المطور بالكامل ---
async def handle_ai_conversation(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    if await maybe_capture_session_name(update, context) or await maybe_capture_rename_name(update, context):
        # تم التعامل مع تسمية الجلسة، ننتظر المدخل التالي
        return AI_CONVERSION

    import random
    thinking_messages = ['أفكر... 🤔', 'أحلل طلبك... 🧠', 'لحظات من فضلك... ✨']
    msg = await update.message.reply_text(random.choice(thinking_messages))
    
    user_text = update.message.text
    user_id = update.effective_user.id
    bucket = _get_user_ai_bucket(context)

    try:
        # --- الخطوة 1: بناء سياق الملف النشط إن وجد ---
        active_file_context = None
        if bucket.get('active_file') and bucket['active_file'].get('chunks'):
            active_file = bucket['active_file']
            search_terms = user_text.lower().split()
            relevant_chunks = [
                chunk for chunk in active_file['chunks']
                if any(term in chunk.lower() for term in search_terms)
            ]
            if relevant_chunks:
                context_text = "\n---\n".join(relevant_chunks[:3]) # نأخذ أفضل 3 أجزاء
                active_file_context = (
                    f"**معلومات ذات صلة من ملف '{active_file['name']}':**\n{context_text}"
                )

        # --- الخطوة 2: بناء البرومبت الكامل باستخدام العقل المدبر ---
        # لاحظ أننا نستخدم الدالة الجديدة enhance_prompt_with_context
        enhanced_prompt = advanced_ai.enhance_prompt_with_context(
            user_input=user_text,
            user_id=user_id,
            history=bucket['history'],
            active_file_context=active_file_context
        )

        # --- الخطوة 3: الحصول على الإجابة (باستخدام الدالة الموحدة) ---
        response_text = await get_gemini_response(enhanced_prompt)
        
        # --- الخطوة 4: تحسين الإجابة النهائية ---
        final_response = add_creative_emojis(response_text) + f"\n\n_{get_encouraging_message()}_"
        await msg.edit_text(final_response, parse_mode='Markdown')

        # --- الخطوة 5: تحديث الذاكرة ---
        bucket['history'].append({'role': 'user', 'content': user_text})
        bucket['history'].append({'role': 'model', 'content': response_text})

        # تشغيل تحديث الذاكرة طويلة المدى في الخلفية
        asyncio.create_task(extract_and_store_facts(user_id, bucket['history']))

    except Exception as e:
        print(f"Error in conversation handler: {e}")
        await msg.edit_text("حدث خطأ غير متوقع. 😵‍💫 الرجاء المحاولة مرة أخرى.")

    return AI_CONVERSION

# --- [دمج] --- معالج الملفات المطور بالكامل ---
async def handle_ai_file_message(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    message = update.message
    # سنركز على PDF في هذا الدمج لتحقيق أفضل تجربة حوارية
    if not (message.document and message.document.file_name.lower().endswith('.pdf') and PyPDF2):
        await update.message.reply_text("حالياً، أنا أركز على تحليل ملفات PDF فقط. 📄 الرجاء إرسال ملف بصيغة PDF لنبدأ حوارنا.")
        return AI_CONVERSION
    
    msg = await update.message.reply_text('جاري قراءة وتحليل ملف الـ PDF... ⏳')
    
    try:
        # --- الخطوة 1: تنزيل الملف ---
        tg_file = await message.document.get_file()
        file_name = message.document.file_name
        os.makedirs('downloads', exist_ok=True)
        local_path = os.path.join('downloads', f"tg_{tg_file.file_unique_id}.pdf")
        await tg_file.download_to_drive(custom_path=local_path)

        # --- الخطوة 2: تقسيم المحتوى إلى أجزاء (Chunks) ---
        file_content_chunks = []
        with open(local_path, 'rb') as f:
            reader = PyPDF2.PdfReader(f)
            for page in reader.pages:
                page_text = page.extract_text()
                if page_text:
                    file_content_chunks.extend([chunk for chunk in page_text.split('\n\n') if len(chunk) > 50])
        
        # --- الخطوة 3: تخزين الأجزاء في جلسة المستخدم ---
        bucket = _get_user_ai_bucket(context)
        bucket['active_file'] = {
            'name': file_name,
            'chunks': file_content_chunks[:150] # نأخذ أول 150 جزء
        }
        
        # --- الخطوة 4: توليد رد ترحيبي ذكي ومخصص للملف ---
        initial_analysis_prompt = (
            f"لقد قمت للتو بتحميل ملف PDF بعنوان '{file_name}'. "
            "بناءً على هذا العنوان، قم بصياغة رسالة ترحيبية قصيرة وذكية للمستخدم، "
            "واقترح عليه 3 أسئلة عامة ومثيرة للاهتمام يمكن أن يبدأ بها نقاشنا حول الملف."
        )
        initial_response = await get_gemini_response(initial_analysis_prompt)
        
        final_message = f"✅ تم تحميل ومعالجة ملف '{file_name}' بنجاح!\n\n" + initial_response
        await msg.edit_text(final_message)
        
        # تنظيف الملف الذي تم تنزيله
        os.remove(local_path)

    except Exception as e:
        print(f"Error in file handler: {e}")
        await msg.edit_text("حدث خطأ أثناء معالجة الملف. 📂 تأكد من أن الملف سليم وحاول مرة أخرى.")
    
    return AI_CONVERSION

# --- أوامر التحكم في الجلسة (تبقى كما هي) ---
# ... (reset_ai_session, export_summary_txt, save_current_session, etc. تبقى كما هي) ...

async def end_ai_chat(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    # --- [دمج] --- تعديل بسيط: نحذف الملف النشط عند الخروج
    bucket = _get_user_ai_bucket(context)
    bucket['active_file'] = None
    await start(update, context)
    return ConversationHandler.END

# --- الدالة الرئيسية (مع تعديل بسيط على معالجات المحادثة) ---
def main() -> None:
    app = Application.builder().token(TELEGRAM_TOKEN).build()
    
    ai_handler = ConversationHandler(
        entry_points=[MessageHandler(filters.Regex('^الدردشة مع الذكاء الاصطناعي 🤖$'), start_ai_chat)],
        states={
            AI_CONVERSION: [
                # --- [دمج] --- تبسيط معالجات المحادثة ---
                # أوامر الأزرار مثل 'أمثلة' و 'إعدادات' تعالج الآن كرسائل نصية عادية
                MessageHandler(filters.Regex('^💡 أمثلة على الأسئلة$'), handle_ai_examples),
                MessageHandler(filters.Regex('^📁 تحليل الملفات$'), handle_file_analysis_info),
                MessageHandler(filters.Regex('^🎯 نصائح للاستخدام$'), handle_usage_tips),
                MessageHandler(filters.Regex('^⚙️ إعدادات الجلسة$'), handle_session_settings),
                
                # المعالج الرئيسي للرسائل النصية
                MessageHandler(filters.TEXT & ~filters.COMMAND, handle_ai_conversation),

                # المعالج الرئيسي للملفات (نركز على PDF)
                MessageHandler(filters.Document.PDF, handle_ai_file_message),
                # يمكنك إضافة معالج للصور هنا بنفس الطريقة إذا أردت
            ]
        },
        fallbacks=[MessageHandler(filters.Regex('^ارجع للقائمة الرئيسية 🔙$'), end_ai_chat)],
    )

    # ... (بقية الـ Handlers كما هي) ...
    app.add_handler(ai_handler)
    # ...
    
    print("🤖 بوت الديوان الجامعي (النسخة الحوارية المدمجة) يعمل الآن...")
    app.run_polling()

if __name__ == '__main__':
    main()